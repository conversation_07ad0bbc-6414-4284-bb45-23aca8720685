

import React, { useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { tokenExpirationService } from '../../services/tokenExpirationService';

interface TokenExpirationMonitorProps {
  children?: React.ReactNode;
}

const TokenExpirationMonitor: React.FC<TokenExpirationMonitorProps> = ({ children }) => {
  const { isAuthenticated, accessToken } = useAuthStore();

  useEffect(() => {
    // Start monitoring when user is authenticated
    if (isAuthenticated && accessToken) {
      console.log('🔒 TokenExpirationMonitor: Starting token expiration monitoring');
      console.log('🔒 Auth state:', { isAuthenticated, hasToken: !!accessToken });
      tokenExpirationService.startMonitoring();

      // Force an immediate check
      setTimeout(() => {
        console.log('🔒 Forcing initial token check');
        tokenExpirationService.forceCheck();
      }, 1000);
    } else {
      // Stop monitoring when user is not authenticated
      console.log('🔒 TokenExpirationMonitor: Stopping token expiration monitoring');
      console.log('🔒 Auth state:', { isAuthenticated, hasToken: !!accessToken });
      tokenExpirationService.stopMonitoring();
    }

    // Cleanup function
    return () => {
    
      // The service will handle stopping when user logs out
    };
  }, [isAuthenticated, accessToken]);

  // Listen for visibility changes to check token when tab becomes active
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && isAuthenticated) {
        // Force check token when tab becomes visible
        tokenExpirationService.forceCheck();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated]);

  // Listen for focus events to check token when window gains focus
  useEffect(() => {
    const handleFocus = () => {
      if (isAuthenticated) {
        // Force check token when window gains focus
        tokenExpirationService.forceCheck();
      }
    };

    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [isAuthenticated]);

  // This component doesn't render anything visible
  return <>{children}</>;
};

export default TokenExpirationMonitor;

/**
 * Hook for accessing token expiration monitoring functionality
 */
export const useTokenExpirationMonitor = () => {
  const { isAuthenticated, accessToken, isTokenExpired } = useAuthStore();

  return {
    isMonitoring: tokenExpirationService.isActive(),
    isAuthenticated,
    hasToken: !!accessToken,
    isTokenExpired: isTokenExpired(),
    forceCheck: () => tokenExpirationService.forceCheck(),

    timeRemaining: tokenExpirationService.getCurrentTokenTimeRemaining(),
  };
};


