{"name": "google-classroom-clone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.2.1", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.24.1", "@types/google.accounts": "^0.0.17", "@types/leaflet": "^1.9.19", "axios": "^1.10.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.9.2", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-query": "^3.39.3", "react-router-dom": "^6.30.1", "tailwind-merge": "^2.2.0", "zod": "^3.24.3", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.14.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.0", "vitest": "^3.2.4"}}