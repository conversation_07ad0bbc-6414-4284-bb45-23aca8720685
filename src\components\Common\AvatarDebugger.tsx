import React, { useState } from 'react';
import { Eye, EyeOff, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { isValidImageUrl, optimizeAvatarUrl, extractPhotoUrl } from '../../utils/avatarUtils';

interface AvatarDebuggerProps {
  user?: any;
  src?: string;
  name?: string;
}

/**
 * Development-only component to help debug avatar image loading issues
 * Only renders in development mode
 */
const AvatarDebugger: React.FC<AvatarDebuggerProps> = ({ user, src, name }) => {
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const extractedUrl = user ? extractPhotoUrl(user) : undefined;
  const finalSrc = src || extractedUrl;
  const optimizedUrl = finalSrc ? optimizeAvatarUrl(finalSrc) : undefined;
  const isValid = finalSrc ? isValidImageUrl(finalSrc) : false;

  const debugInfo = {
    userName: name || user?.name || 'Unknown',
    originalSrc: src,
    extractedFromUser: extractedUrl,
    finalSrc,
    optimizedUrl,
    isValid,
    userFields: user ? Object.keys(user).filter(key => 
      typeof user[key] === 'string' && (
        key.toLowerCase().includes('photo') ||
        key.toLowerCase().includes('avatar') ||
        key.toLowerCase().includes('picture') ||
        key.toLowerCase().includes('image')
      )
    ) : []
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 z-50"
        title="Show Avatar Debug Info"
      >
        <Eye size={16} />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-gray-900">Avatar Debug Info</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <EyeOff size={16} />
        </button>
      </div>

      <div className="space-y-2 text-sm">
        <div>
          <strong>User:</strong> {debugInfo.userName}
        </div>

        <div className="flex items-center gap-2">
          <strong>Valid URL:</strong>
          {isValid ? (
            <CheckCircle size={16} className="text-green-500" />
          ) : (
            <XCircle size={16} className="text-red-500" />
          )}
          <span className={isValid ? 'text-green-600' : 'text-red-600'}>
            {isValid ? 'Yes' : 'No'}
          </span>
        </div>

        {debugInfo.originalSrc && (
          <div>
            <strong>Original Src:</strong>
            <div className="text-xs text-gray-600 break-all bg-gray-50 p-1 rounded">
              {debugInfo.originalSrc}
            </div>
          </div>
        )}

        {debugInfo.extractedFromUser && (
          <div>
            <strong>Extracted from User:</strong>
            <div className="text-xs text-gray-600 break-all bg-gray-50 p-1 rounded">
              {debugInfo.extractedFromUser}
            </div>
          </div>
        )}

        {debugInfo.optimizedUrl && (
          <div>
            <strong>Optimized URL:</strong>
            <div className="text-xs text-gray-600 break-all bg-gray-50 p-1 rounded">
              {debugInfo.optimizedUrl}
            </div>
          </div>
        )}

        {debugInfo.userFields.length > 0 && (
          <div>
            <strong>Available Image Fields:</strong>
            <div className="text-xs text-gray-600">
              {debugInfo.userFields.join(', ')}
            </div>
          </div>
        )}

        {!debugInfo.finalSrc && (
          <div className="flex items-center gap-2 text-amber-600">
            <AlertCircle size={16} />
            <span>No image URL found</span>
          </div>
        )}

        {debugInfo.finalSrc && !isValid && (
          <div className="flex items-center gap-2 text-red-600">
            <XCircle size={16} />
            <span>Invalid image URL format</span>
          </div>
        )}

        {optimizedUrl && (
          <div className="mt-3">
            <strong>Test Image:</strong>
            <div className="mt-1">
              <img
                src={optimizedUrl}
                alt="Test"
                className="w-12 h-12 rounded-full object-cover border"
                onLoad={() => console.log('✅ Debug image loaded successfully')}
                onError={() => console.error('❌ Debug image failed to load')}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AvatarDebugger;
