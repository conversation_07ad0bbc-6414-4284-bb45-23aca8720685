import React from 'react';
import Avatar from './Avatar';

/**
 * Test component to verify avatar functionality with various URL types
 * Only renders in development mode
 */
const AvatarTest: React.FC = () => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const testCases = [
    {
      name: 'Valid Google Image',
      src: 'https://lh3.googleusercontent.com/a/ACg8ocIY26fr-P6Wz3d7EUfcBbsiwsstm4j0NnZZDcSD5RWMigRl8SE',
      user: { name: '<PERSON>', photoUrl: 'https://lh3.googleusercontent.com/a/ACg8ocIY26fr-P6Wz3d7EUfcBbsiwsstm4j0NnZZDcSD5RWMigRl8SE' }
    },
    {
      name: 'Invalid URL',
      src: 'https://invalid-domain-that-does-not-exist.com/image.jpg',
      user: { name: '<PERSON>', avatar: 'https://invalid-domain-that-does-not-exist.com/image.jpg' }
    },
    {
      name: 'No Image - Initials Only',
      src: undefined,
      user: { name: '<PERSON>' }
    },
    {
      name: 'Gravatar',
      src: 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50',
      user: { name: 'Alice Brown', picture: 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50' }
    },
    {
      name: 'Empty User',
      src: undefined,
      user: {}
    }
  ];

  return (
    <div className="fixed top-4 left-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <h3 className="font-semibold text-gray-900 mb-3">Avatar Test Cases</h3>
      <div className="space-y-3">
        {testCases.map((testCase, index) => (
          <div key={index} className="flex items-center space-x-3">
            <Avatar
              src={testCase.src}
              name={testCase.user.name}
              user={testCase.user}
              size="md"
              showDebugger={false} // Don't show debugger for test cases
            />
            <div className="text-sm">
              <div className="font-medium">{testCase.name}</div>
              <div className="text-gray-500 text-xs">
                {testCase.user.name || 'No name'}
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-3 text-xs text-gray-500">
        Check console for debug logs
      </div>
    </div>
  );
};

export default AvatarTest;
