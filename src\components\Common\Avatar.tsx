import React, { useState } from 'react';
import { User } from 'lucide-react';
import {
  optimizeAvatarUrl,
  getInitials,
  getAvatarBackgroundColor,
  getAvatarTextColor
} from '../../utils/avatarUtils';
import AvatarDebugger from './AvatarDebugger';

interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  user?: any; // For debugging purposes
  showDebugger?: boolean; // Enable debugger in development
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name,
  size = 'md',
  className = '',
  user,
  showDebugger = false
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(!!src);

  // Optimize the image URL for better performance and reliability
  const optimizedSrc = src ? optimizeAvatarUrl(src) : undefined;

  // Reset states when src changes
  React.useEffect(() => {
    if (src) {
      setImageError(false);
      setImageLoading(true);
    } else {
      setImageLoading(false);
    }
  }, [src]);



  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 12,
    md: 16,
    lg: 20,
    xl: 24
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    if (process.env.NODE_ENV === 'development') {
      console.log('Avatar: Image loaded successfully:', optimizedSrc || src);
    }
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const imgElement = event.currentTarget;
    const failedUrl = imgElement.src;

    if (process.env.NODE_ENV === 'development') {
      console.warn('Avatar: Image failed to load:', {
        originalSrc: src,
        optimizedSrc,
        failedUrl,
        userName: name,
        error: 'Image load error - possibly CORS, 404, or invalid format'
      });
    }

    setImageError(true);
    setImageLoading(false);
  };

  const baseClasses = `${sizeClasses[size]} rounded-full object-cover ${className}`;

  // Get dynamic background and text colors based on name
  const backgroundColor = getAvatarBackgroundColor(name);
  const textColor = getAvatarTextColor(backgroundColor);

  // Show image if we have a valid optimized URL and no error occurred
  if (optimizedSrc && !imageError) {
    return (
      <div className="relative">
        <img
          src={optimizedSrc}
          alt={alt || name || 'User avatar'}
          className={baseClasses}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy"
        />
        {imageLoading && (
          <div className={`${baseClasses} ${backgroundColor} animate-pulse flex items-center justify-center absolute inset-0`}>
            <User size={iconSizes[size]} className={textColor.replace('text-', '').replace('-600', '-400')} />
          </div>
        )}
      </div>
    );
  }

  // Show initials if name is available
  if (name && getInitials(name)) {
    return (
      <div className={`${baseClasses} ${backgroundColor} flex items-center justify-center ${textColor} font-medium`}>
        <span className={`text-${size === 'sm' ? 'xs' : size === 'md' ? 'sm' : 'base'}`}>
          {getInitials(name)}
        </span>
      </div>
    );
  }

  // Fallback to default user icon
  return (
    <>
      <div className={`${baseClasses} ${backgroundColor} flex items-center justify-center`}>
        <User size={iconSizes[size]} className={textColor} />
      </div>
      {showDebugger && (
        <AvatarDebugger user={user} src={src} name={name} />
      )}
    </>
  );
};

export default Avatar;